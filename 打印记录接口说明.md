# 打印记录接口说明

## 概述
新增了一个打印记录接口，用于记录用户的打印操作信息，包括打印设备、模版、份数和结果等关键信息。

## 接口详情

### 添加打印记录
**接口地址：** `POST /PrinterManager/AddPrintRecord`

**请求参数：**
```json
{
  "head": {
    "requestId": "请求ID",
    "uid": "用户ID", 
    "appId": 1
  },
  "body": {
    "openId": "微信OpenId（必填）",
    "printerSN": "打印设备SN（必填，最大100字符）",
    "templateId": 123,  // 打印模版id（必填，大于0）
    "printCount": 2,    // 打印份数（必填，大于0）
    "resultCode": "SUCCESS",  // 打印结果编码（必填）
    "resultDescription": "打印成功"  // 打印结果描述（必填，最大500字符）
  }
}
```

**响应参数：**
```json
{
  "head": {
    "message": "添加打印记录成功",
    "code": "200",
    "callTime": "2024-01-01 12:00:00"
  },
  "result": true
}
```

**错误响应示例：**
```json
{
  "head": {
    "message": "参数错误",
    "code": "2001",
    "callTime": "2024-01-01 12:00:00"
  },
  "result": false
}
```

## 数据库表结构

### ext_print_record 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键，自增 |
| open_id | varchar(100) | 微信OpenId |
| printer_sn | varchar(100) | 打印设备SN |
| template_id | bigint(20) | 打印模版id |
| print_count | int(11) | 打印份数 |
| result_code | varchar(50) | 打印结果编码 |
| result_description | varchar(500) | 打印结果描述 |
| print_time | datetime | 打印时间 |
| created_by | varchar(50) | 创建人 |
| creator_id | varchar(50) | 创建人Id |
| created_time | datetime | 创建时间 |
| updated_by | varchar(50) | 更新人 |
| modifier_id | varchar(50) | 更新人Id |
| updated_time | datetime | 修改时间 |
| is_deleted | tinyint(1) | 逻辑删除标记 |

## 实现的文件

### 新增文件：
1. `Welshine.Official.Domain\Entity\PrintRecord.cs` - 打印记录实体类
2. `Welshine.Official.Repository\Interface\IPrintRecordRepository.cs` - 打印记录仓储接口
3. `Welshine.Official.Repository\PrintRecordRepository.cs` - 打印记录仓储实现
4. `database_scripts\create_print_record_table.sql` - 数据库表创建脚本
5. `TestProject1\PrintRecordTest.cs` - 单元测试
6. `api_examples\add_print_record_example.json` - API请求示例

### 修改文件：
1. `Welshine.Official.Domain\VO\App\Request\LabelRequests.cs` - 添加了AddPrintRecordRequest请求模型
2. `Welshine.Official.Service\Interface\IPrinterManagerService.cs` - 添加了AddPrintRecord方法
3. `Welshine.Official.Service\PrinterManagerService.cs` - 实现了AddPrintRecord方法
4. `Welshine.Official.WxApp.Api\Controllers\PrinterManagerController.cs` - 添加了AddPrintRecord接口

## 使用说明

1. **部署前准备：**
   - 执行 `database_scripts\create_print_record_table.sql` 创建数据库表

2. **接口调用：**
   - 使用POST方法调用 `/PrinterManager/AddPrintRecord` 接口
   - 传入完整的请求参数，包括head和body部分
   - 所有必填字段都需要提供有效值

3. **参数验证：**
   - openId、printerSN、resultCode、resultDescription 不能为空
   - templateId 和 printCount 必须大于0
   - printerSN 最大长度100字符
   - resultDescription 最大长度500字符

4. **错误处理：**
   - 参数验证失败会返回业务异常
   - 数据库操作失败会返回系统错误

## 测试

运行单元测试：
```bash
dotnet test TestProject1/PrintRecordTest.cs
```

测试覆盖了以下场景：
- 正常添加打印记录
- 空OpenId参数验证
- 无效打印份数参数验证

## 注意事项

1. 该接口依赖于现有的依赖注入配置，无需额外配置
2. 打印时间会自动设置为当前时间
3. 实体类继承了BaseBigEntity，包含了标准的审计字段
4. 所有Repository和Service都会通过Autofac自动注册
