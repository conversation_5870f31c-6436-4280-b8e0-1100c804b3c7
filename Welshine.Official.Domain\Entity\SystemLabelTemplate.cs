using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 系统默认标签模版表（系统提供的预设模版）
    /// </summary>
    [SqlSugar.SugarTable("ext_system_label_template")]
    public class SystemLabelTemplate : BaseBigEntity
    {
        /// <summary>
        /// 模版名称
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "template_name")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 模版内容（JSON 字符串）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "template_content", Length = 0)]
        public string TemplateContent { get; set; }

        /// <summary>
        /// 模版缩略图（Base64）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "template_thumbnail", ColumnDataType = "longtext")]
        public string TemplateThumbnail { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "category_name")]
        public string CategoryName { get; set; }

        /// <summary>
        /// 模版描述
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "template_description")]
        public string TemplateDescription { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "sort")]
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 标签尺寸（如：100x50mm）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "label_size")]
        public string LabelSize { get; set; }

        /// <summary>
        /// 适用场景标签（如：商品,价格,地址等）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "scene_tags")]
        public string SceneTags { get; set; }
    }
}
