using System.ComponentModel.DataAnnotations;

namespace Welshine.Official.Domain.VO.App.Request
{
    public class AddLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeleteLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    public class AddPrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public string Json { get; set; }
    }

    public class GetPrintedLabelsRequest
    {
        [Required]
        public string OpenId { get; set; }
    }

    public class DeletePrintedLabelRequest
    {
        [Required]
        public string OpenId { get; set; }
        [Required]
        public long LabelId { get; set; }
    }

    /// <summary>
    /// 添加打印记录请求
    /// </summary>
    public class AddPrintRecordRequest
    {
        /// <summary>
        /// 微信 OpenId
        /// </summary>
        [Required(ErrorMessage = "OpenId是必填项", AllowEmptyStrings = false)]
        public string OpenId { get; set; }

        /// <summary>
        /// 打印设备SN
        /// </summary>
        [Required(ErrorMessage = "打印设备SN是必填项", AllowEmptyStrings = false)]
        [StringLength(100, ErrorMessage = "打印设备SN长度不能超过100个字符", MinimumLength = 1)]
        public string PrinterSN { get; set; }

        /// <summary>
        /// 打印模版id
        /// </summary>
        [Required(ErrorMessage = "打印模版id是必填项")]
        public long TemplateId { get; set; }

        /// <summary>
        /// 打印份数
        /// </summary>
        [Required(ErrorMessage = "打印份数是必填项")]
        [Range(1, int.MaxValue, ErrorMessage = "打印份数必须大于0")]
        public int PrintCount { get; set; }

        /// <summary>
        /// 打印结果编码
        /// </summary>
        [Required(ErrorMessage = "打印结果编码是必填项", AllowEmptyStrings = false)]
        public string ResultCode { get; set; }

        /// <summary>
        /// 打印结果描述
        /// </summary>
        [Required(ErrorMessage = "打印结果描述是必填项", AllowEmptyStrings = false)]
        [StringLength(500, ErrorMessage = "打印结果描述长度不能超过500个字符")]
        public string ResultDescription { get; set; }
    }

    /// <summary>
    /// 获取系统标签模版分类请求
    /// </summary>
    public class GetSystemLabelTemplateCategoriesRequest
    {
        // 无需参数，获取所有分类
    }

    /// <summary>
    /// 通过分类获取系统标签模版请求
    /// </summary>
    public class GetSystemLabelTemplatesByCategoryRequest
    {
        /// <summary>
        /// 分类名称，为空则获取全部系统模版
        /// </summary>
        public string CategoryName { get; set; }
    }

    /// <summary>
    /// 搜索系统标签模版请求
    /// </summary>
    public class SearchSystemLabelTemplatesRequest
    {
        /// <summary>
        /// 模版名称（模糊搜索）
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 标签尺寸（精确匹配）
        /// </summary>
        public string LabelSize { get; set; }

        /// <summary>
        /// 适用场景标签（模糊搜索，支持多个关键词用逗号分隔）
        /// </summary>
        public string SceneTags { get; set; }

        /// <summary>
        /// 分类名称（可选）
        /// </summary>
        public string CategoryName { get; set; }
    }
}

