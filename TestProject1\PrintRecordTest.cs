using System;
using System.Threading.Tasks;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service;
using Welshine.Official.Service.Interface;
using Xunit;
using Moq;
using AutoMapper;

namespace TestProject1
{
    /// <summary>
    /// 打印记录功能测试
    /// </summary>
    public class PrintRecordTest
    {
        [Fact]
        public async Task AddPrintRecord_ValidInput_ShouldReturnTrue()
        {
            // Arrange
            var mockPrinterDeviceRepo = new Mock<IPrinterDeviceRepository>();
            var mockPrintRecordRepo = new Mock<IPrintRecordRepository>();
            var mockMapper = new Mock<IMapper>();

            // 设置模拟返回值
            mockPrintRecordRepo.Setup(x => x.Add(It.IsAny<PrintRecord>()))
                              .ReturnsAsync(true);

            var service = new PrinterManagerService(
                mockPrinterDeviceRepo.Object,
                mockPrintRecordRepo.Object,
                mockMapper.Object);

            // Act
            var result = await service.AddPrintRecord(
                "test_open_id",
                "TEST_SN_001",
                123,
                2,
                "SUCCESS",
                "打印成功");

            // Assert
            Assert.True(result);
            mockPrintRecordRepo.Verify(x => x.Add(It.Is<PrintRecord>(p =>
                p.OpenId == "test_open_id" &&
                p.PrinterSN == "TEST_SN_001" &&
                p.TemplateId == 123 &&
                p.PrintCount == 2 &&
                p.ResultCode == "SUCCESS" &&
                p.ResultDescription == "打印成功"
            )), Times.Once);
        }

        [Fact]
        public async Task AddPrintRecord_EmptyOpenId_ShouldThrowException()
        {
            // Arrange
            var mockPrinterDeviceRepo = new Mock<IPrinterDeviceRepository>();
            var mockPrintRecordRepo = new Mock<IPrintRecordRepository>();
            var mockMapper = new Mock<IMapper>();

            var service = new PrinterManagerService(
                mockPrinterDeviceRepo.Object,
                mockPrintRecordRepo.Object,
                mockMapper.Object);

            // Act & Assert
            await Assert.ThrowsAsync<Welshine.Official.Core.Exceptions.BusinessException>(
                () => service.AddPrintRecord("", "TEST_SN_001", 123, 2, "SUCCESS", "打印成功"));
        }

        [Fact]
        public async Task AddPrintRecord_InvalidPrintCount_ShouldThrowException()
        {
            // Arrange
            var mockPrinterDeviceRepo = new Mock<IPrinterDeviceRepository>();
            var mockPrintRecordRepo = new Mock<IPrintRecordRepository>();
            var mockMapper = new Mock<IMapper>();

            var service = new PrinterManagerService(
                mockPrinterDeviceRepo.Object,
                mockPrintRecordRepo.Object,
                mockMapper.Object);

            // Act & Assert
            await Assert.ThrowsAsync<Welshine.Official.Core.Exceptions.BusinessException>(
                () => service.AddPrintRecord("test_open_id", "TEST_SN_001", 123, 0, "SUCCESS", "打印成功"));
        }
    }
}
