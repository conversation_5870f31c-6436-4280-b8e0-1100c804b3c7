using AutoMapper;
using System;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 打印机管理服务实现
    /// </summary>
    public class PrinterManagerService : IPrinterManagerService
    {
        private readonly IPrinterDeviceRepository _printerDeviceRepository;
        private readonly IPrintRecordRepository _printRecordRepository;
        private readonly IMapper _mapper;

        public PrinterManagerService(IPrinterDeviceRepository printerDeviceRepository, IPrintRecordRepository printRecordRepository, IMapper mapper)
        {
            _printerDeviceRepository = printerDeviceRepository;
            _printRecordRepository = printRecordRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// 检查打印机设备是否在库
        /// </summary>
        /// <param name="model">打印机型号</param>
        /// <param name="sn">设备SN码</param>
        /// <returns></returns>
        public async Task<CheckPrinterDeviceResponse> CheckPrinterDevice(string model, string sn)
        {
            if (string.IsNullOrWhiteSpace(model))
                throw new BusinessException(ErrorCode.PrinterModelNotEmpty.ToDescriptionName(), ErrorCode.PrinterModelNotEmpty.GetHashCode());

            if (string.IsNullOrWhiteSpace(sn))
                throw new BusinessException(ErrorCode.PrinterSnNotEmpty.ToDescriptionName(), ErrorCode.PrinterSnNotEmpty.GetHashCode());

            // 查询设备信息
            var device = await _printerDeviceRepository.GetByModelAndSN(model, sn);
            
            if (device == null)
            {
                throw new BusinessException(ErrorCode.PrinterNotExist.ToDescriptionName(), ErrorCode.PrinterNotExist.GetHashCode());
            }
            else
            {
                // 设备存在，更新查询记录
                await _printerDeviceRepository.UpdateQueryRecord(device);
            }

            // 构造响应
            var response = new CheckPrinterDeviceResponse
            {
                Model = device.Model,
                SN = device.SN,
                IsInStock = device.DeviceStatus == 1,
                FirstQueryTime = device.FirstQueryTime,
                LastQueryTime = device.LastQueryTime,
                QueryCount = device.QueryCount,
                Remark = device.Remark
            };

            return response;
        }

        /// <summary>
        /// 添加打印记录
        /// </summary>
        /// <param name="openId">微信OpenId</param>
        /// <param name="printerSN">打印设备SN</param>
        /// <param name="templateId">打印模版id</param>
        /// <param name="printCount">打印份数</param>
        /// <param name="resultCode">打印结果编码</param>
        /// <param name="resultDescription">打印结果描述</param>
        /// <returns></returns>
        public async Task<bool> AddPrintRecord(string openId, string printerSN, long templateId, int printCount, string resultCode, string resultDescription)
        {
            if (string.IsNullOrWhiteSpace(openId))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            if (string.IsNullOrWhiteSpace(printerSN))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            if (templateId <= 0)
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            if (printCount <= 0)
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            if (string.IsNullOrWhiteSpace(resultCode))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            if (string.IsNullOrWhiteSpace(resultDescription))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            var printRecord = new PrintRecord
            {
                OpenId = openId,
                PrinterSN = printerSN,
                TemplateId = templateId,
                PrintCount = printCount,
                ResultCode = resultCode,
                ResultDescription = resultDescription,
                PrintTime = DateTime.Now
            };

            return await _printRecordRepository.Add(printRecord);
        }
    }
}
