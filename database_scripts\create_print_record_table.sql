-- 创建打印记录表
CREATE TABLE `ext_print_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `open_id` varchar(100) NOT NULL COMMENT '微信 OpenId',
  `printer_sn` varchar(100) NOT NULL COMMENT '打印设备SN',
  `template_id` bigint(20) NOT NULL COMMENT '打印模版id',
  `print_count` int(11) NOT NULL COMMENT '打印份数',
  `result_code` varchar(50) NOT NULL COMMENT '打印结果编码',
  `result_description` varchar(500) NOT NULL COMMENT '打印结果描述',
  `print_time` datetime NOT NULL COMMENT '打印时间',
  `created_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `creator_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '创建人Id',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `modifier_id` varchar(50) NOT NULL DEFAULT '0' COMMENT '更新人Id',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_printer_sn` (`printer_sn`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_print_time` (`print_time`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打印记录表';
