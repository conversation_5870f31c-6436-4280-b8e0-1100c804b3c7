using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.Entity;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Repository.Interface;
using Welshine.Official.Service.Interface;
using SqlSugar.IOC;
using SqlSugar;

namespace Welshine.Official.Service
{
    /// <summary>
    /// 标签服务
    /// </summary>
    public class LabelService : ILabelService
    {
        private readonly ILabelTemplateRepository _labelTemplateRepository;
        private readonly IPrintedLabelRepository _printedLabelRepository;

        public LabelService(ILabelTemplateRepository labelTemplateRepository, IPrintedLabelRepository printedLabelRepository)
        {
            _labelTemplateRepository = labelTemplateRepository;
            _printedLabelRepository = printedLabelRepository;
        }

        public async Task<bool> AddLabelAsync(string openId, string jsonContent)
        {
            if (string.IsNullOrWhiteSpace(openId))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());
            if (string.IsNullOrWhiteSpace(jsonContent))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            var entity = new LabelTemplate
            {
                OpenId = openId,
                JsonContent = jsonContent
            };
            return await _labelTemplateRepository.Add(entity);
        }

        public async Task<PageRows<LabelTemplate>> GetLabelsAsync(string openId, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var list = await DbScoped.SugarScope.Queryable<LabelTemplate>()
                .Where(x => !x.IsDeleted && x.OpenId == openId)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .ToPageListAsync(pageIndex, pageSize, total);
            return new PageRows<LabelTemplate> { Data = list, Total = total };
        }

        public async Task<bool> DeleteLabelAsync(string openId, long labelId)
        {
            var entity = await _labelTemplateRepository.QueryByID(labelId);
            if (entity == null || entity.IsDeleted || entity.OpenId != openId) return false;
            entity.IsDeleted = true;
            entity.UpdatedTime = DateTime.Now;
            return await _labelTemplateRepository.Update(entity);
        }

        public async Task<bool> AddPrintedLabelAsync(string openId, string jsonContent)
        {
            if (string.IsNullOrWhiteSpace(openId))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());
            if (string.IsNullOrWhiteSpace(jsonContent))
                throw new BusinessException(ErrorCode.Param.ToDescriptionName(), ErrorCode.Param.GetHashCode());

            var entity = new PrintedLabel
            {
                OpenId = openId,
                JsonContent = jsonContent
            };
            return await _printedLabelRepository.Add(entity);
        }

        public async Task<PageRows<PrintedLabel>> GetPrintedLabelsAsync(string openId, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var list = await DbScoped.SugarScope.Queryable<PrintedLabel>()
                .Where(x => !x.IsDeleted && x.OpenId == openId)
                .OrderBy(x => x.UpdatedTime, OrderByType.Desc)
                .ToPageListAsync(pageIndex, pageSize, total);
            return new PageRows<PrintedLabel> { Data = list, Total = total };
        }

        public async Task<bool> DeletePrintedLabelAsync(string openId, long labelId)
        {
            var entity = await _printedLabelRepository.QueryByID(labelId);
            if (entity == null || entity.IsDeleted || entity.OpenId != openId) return false;
            entity.IsDeleted = true;
            entity.UpdatedTime = DateTime.Now;
            return await _printedLabelRepository.Update(entity);
        }

        /// <summary>
        /// 获取系统标签模版分类列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<LabelTemplateCategoryResponse>> GetSystemLabelTemplateCategoriesAsync()
        {
            var categories = await DbScoped.SugarScope.Queryable<LabelTemplateCategory>()
                .Where(x => !x.IsDeleted && x.IsEnabled)
                .OrderBy(x => x.Sort)
                .OrderBy(x => x.Id)
                .ToListAsync();

            return categories.Select(x => new LabelTemplateCategoryResponse
            {
                CategoryId = x.Id,
                CategoryName = x.CategoryName
            }).ToList();
        }

        /// <summary>
        /// 通过分类获取系统标签模版列表（分页）
        /// </summary>
        /// <param name="categoryId">分类ID，为空则获取全部系统模版</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns></returns>
        public async Task<PageRows<SystemLabelTemplateResponse>> GetSystemLabelTemplatesByCategoryAsync(string categoryName, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var query = DbScoped.SugarScope.Queryable<SystemLabelTemplate>()
                .Where(x => !x.IsDeleted && x.IsEnabled);

            // 如果指定了分类ID，则按分类筛选
            if (!string.IsNullOrEmpty(categoryName))
            {
                query = query.Where(x => x.CategoryName == categoryName);
            }

            var list = await query
                .OrderBy(x => x.Sort)
                .OrderBy(x => x.Id)
                .ToPageListAsync(pageIndex, pageSize, total);

            var result = list.Select(x => new SystemLabelTemplateResponse
            {
                TemplateId = x.Id,
                TemplateName = x.TemplateName,
                TemplateContent = x.TemplateContent,
                TemplateThumbnail = x.TemplateThumbnail,
                TemplateDescription = x.TemplateDescription,
                LabelSize = x.LabelSize,
                SceneTags = x.SceneTags
            }).ToList();

            return new PageRows<SystemLabelTemplateResponse> { Data = result, Total = total };
        }

        /// <summary>
        /// 搜索系统标签模版列表（分页）
        /// </summary>
        /// <param name="templateName">模版名称（模糊搜索）</param>
        /// <param name="labelSize">标签尺寸（精确匹配）</param>
        /// <param name="sceneTags">适用场景标签（模糊搜索）</param>
        /// <param name="categoryId">分类ID（可选）</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns></returns>
        public async Task<PageRows<SystemLabelTemplateResponse>> SearchSystemLabelTemplatesAsync(string templateName, string labelSize, string sceneTags, string categoryName, int pageIndex, int pageSize)
        {
            RefAsync<int> total = 0;
            var query = DbScoped.SugarScope.Queryable<SystemLabelTemplate>()
                .Where(x => !x.IsDeleted && x.IsEnabled);

            // 按分类筛选
            if (string.IsNullOrEmpty(categoryName))
            {
                query = query.Where(x => x.CategoryName == categoryName);
            }

            // 按模版名称模糊搜索
            if (!string.IsNullOrWhiteSpace(templateName))
            {
                query = query.Where(x => x.TemplateName.Contains(templateName));
            }

            // 按标签尺寸精确匹配
            if (!string.IsNullOrWhiteSpace(labelSize))
            {
                query = query.Where(x => x.LabelSize == labelSize);
            }

            // 按适用场景标签模糊搜索
            if (!string.IsNullOrWhiteSpace(sceneTags))
            {
                // 支持多个关键词搜索，用逗号分隔
                var keywords = sceneTags.Split(',', '，').Where(k => !string.IsNullOrWhiteSpace(k)).ToArray();
                if (keywords.Length > 0)
                {
                    // 任意一个关键词匹配即可
                    var sceneCondition = keywords.Select(keyword =>
                        $"scene_tags LIKE '%{keyword.Trim()}%'").ToArray();
                    var whereClause = string.Join(" OR ", sceneCondition);
                    query = query.Where($"({whereClause})");
                }
            }

            var list = await query
                .OrderBy(x => x.Sort)
                .OrderBy(x => x.Id)
                .ToPageListAsync(pageIndex, pageSize, total);

            var result = list.Select(x => new SystemLabelTemplateResponse
            {
                TemplateId = x.Id,
                TemplateName = x.TemplateName,
                TemplateContent = x.TemplateContent,
                TemplateThumbnail = x.TemplateThumbnail,
                TemplateDescription = x.TemplateDescription,
                LabelSize = x.LabelSize,
                SceneTags = x.SceneTags
            }).ToList();

            return new PageRows<SystemLabelTemplateResponse> { Data = result, Total = total };
        }
    }
}

